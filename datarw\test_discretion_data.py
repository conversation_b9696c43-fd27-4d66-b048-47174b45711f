"""
自由裁量权数据处理模块测试脚本
用于测试 discretion_data.py 模块的功能
"""

import os
import json
import unittest
from discretion_data import DiscretionDataProcessor


class TestDiscretionDataProcessor(unittest.TestCase):
    """自由裁量权数据处理器测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.test_file = "datarw/data/4675378.docx"
        self.output_file = "datarw/data/test_output.json"
        self.processor = DiscretionDataProcessor(self.test_file)
    
    def tearDown(self):
        """测试后的清理工作"""
        if os.path.exists(self.output_file):
            os.remove(self.output_file)
    
    def test_load_document(self):
        """测试文档加载功能"""
        result = self.processor.load_document()
        self.assertTrue(result, "文档加载应该成功")
        self.assertIsNotNone(self.processor.document, "文档对象不应为空")
    
    def test_load_nonexistent_document(self):
        """测试加载不存在的文档"""
        processor = DiscretionDataProcessor("nonexistent.docx")
        result = processor.load_document()
        self.assertFalse(result, "加载不存在的文档应该失败")
    
    def test_process_all_tables(self):
        """测试表格处理功能"""
        self.processor.load_document()
        tables_data = self.processor.process_all_tables()
        
        self.assertIsInstance(tables_data, list, "返回结果应该是列表")
        self.assertGreater(len(tables_data), 0, "应该至少有一个表格")
        
        # 检查第一个表格的结构
        first_table = tables_data[0]
        self.assertIn('表格编号', first_table, "表格应该有编号")
        self.assertIn('表格行数', first_table, "表格应该有行数")
        self.assertIn('表格列数', first_table, "表格应该有列数")
        self.assertIn('数据', first_table, "表格应该有数据")
        
        # 检查数据结构
        data = first_table['数据']
        self.assertIsInstance(data, list, "数据应该是列表")
        if data:
            first_row = data[0]
            self.assertIn('行号', first_row, "每行数据应该有行号")
    
    def test_save_to_json(self):
        """测试 JSON 保存功能"""
        self.processor.load_document()
        self.processor.process_all_tables()
        
        result = self.processor.save_to_json(self.output_file)
        self.assertTrue(result, "JSON 保存应该成功")
        self.assertTrue(os.path.exists(self.output_file), "输出文件应该存在")
        
        # 验证 JSON 文件内容
        with open(self.output_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.assertIn('文档信息', data, "JSON 应该包含文档信息")
        self.assertIn('表格数据', data, "JSON 应该包含表格数据")
        
        doc_info = data['文档信息']
        self.assertIn('源文件', doc_info, "文档信息应该包含源文件")
        self.assertIn('表格数量', doc_info, "文档信息应该包含表格数量")
        self.assertIn('处理时间', doc_info, "文档信息应该包含处理时间")
    
    def test_get_summary(self):
        """测试摘要功能"""
        self.processor.load_document()
        self.processor.process_all_tables()
        
        summary = self.processor.get_summary()
        
        self.assertIn('文档路径', summary, "摘要应该包含文档路径")
        self.assertIn('表格总数', summary, "摘要应该包含表格总数")
        self.assertIn('数据总行数', summary, "摘要应该包含数据总行数")
        self.assertIn('表格详情', summary, "摘要应该包含表格详情")
        
        self.assertIsInstance(summary['表格详情'], list, "表格详情应该是列表")


def run_integration_test():
    """运行集成测试"""
    print("开始运行集成测试...")
    
    # 测试完整的处理流程
    processor = DiscretionDataProcessor("datarw/data/4675378.docx")
    
    print("1. 加载文档...")
    if not processor.load_document():
        print("❌ 文档加载失败")
        return False
    print("✅ 文档加载成功")
    
    print("2. 处理表格...")
    tables_data = processor.process_all_tables()
    if not tables_data:
        print("❌ 表格处理失败")
        return False
    print(f"✅ 成功处理 {len(tables_data)} 个表格")
    
    print("3. 保存 JSON...")
    output_file = "datarw/data/integration_test_output.json"
    if not processor.save_to_json(output_file):
        print("❌ JSON 保存失败")
        return False
    print(f"✅ JSON 保存成功: {output_file}")
    
    print("4. 生成摘要...")
    summary = processor.get_summary()
    print("✅ 处理摘要:")
    print(json.dumps(summary, ensure_ascii=False, indent=2))
    
    # 清理测试文件
    if os.path.exists(output_file):
        os.remove(output_file)
    
    print("\n🎉 集成测试完成！")
    return True


if __name__ == "__main__":
    print("=" * 50)
    print("自由裁量权数据处理模块测试")
    print("=" * 50)
    
    # 运行单元测试
    print("\n📋 运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 50)
    
    # 运行集成测试
    print("\n🔧 运行集成测试...")
    run_integration_test()
