"""
自由裁量权数据处理模块
用于读取和解析 Word 文档中的表格数据，并将其转换为 JSON 格式
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any
from docx import Document
from docx.table import Table
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DiscretionDataProcessor:
    """自由裁量权数据处理器"""

    def __init__(self, file_path: str):
        """
        初始化数据处理器

        Args:
            file_path: Word 文档文件路径
        """
        self.file_path = file_path
        self.document = None
        self.tables_data = []

    def load_document(self) -> bool:
        """
        加载 Word 文档

        Returns:
            bool: 加载成功返回 True，失败返回 False
        """
        try:
            if not os.path.exists(self.file_path):
                logger.error(f"文件不存在: {self.file_path}")
                return False

            self.document = Document(self.file_path)
            logger.info(f"成功加载文档: {self.file_path}")
            return True

        except Exception as e:
            logger.error(f"加载文档失败: {str(e)}")
            return False

    def extract_table_data(self, table: Table) -> List[Dict[str, Any]]:
        """
        从表格中提取数据

        Args:
            table: Word 文档中的表格对象

        Returns:
            List[Dict]: 表格数据列表，每行数据为一个字典
        """
        table_data = []

        if not table.rows:
            logger.warning("表格为空")
            return table_data

        # 获取表头
        header_row = table.rows[0]
        headers = []
        for cell in header_row.cells:
            header_text = cell.text.strip()
            headers.append(header_text if header_text else f"列_{len(headers) + 1}")

        logger.info(f"表格表头: {headers}")

        # 提取数据行
        for row_idx, row in enumerate(table.rows[1:], start=1):
            row_data = {}
            for col_idx, cell in enumerate(row.cells):
                if col_idx < len(headers):
                    cell_text = cell.text.strip()
                    row_data[headers[col_idx]] = cell_text

            # 只添加非空行
            if any(value.strip() for value in row_data.values()):
                row_data['行号'] = row_idx
                table_data.append(row_data)

        logger.info(f"提取到 {len(table_data)} 行数据")
        return table_data

    def process_all_tables(self) -> List[Dict[str, Any]]:
        """
        处理文档中的所有表格

        Returns:
            List[Dict]: 所有表格的数据
        """
        if not self.document:
            logger.error("文档未加载，请先调用 load_document()")
            return []

        all_tables_data = []

        for table_idx, table in enumerate(self.document.tables):
            logger.info(f"正在处理第 {table_idx + 1} 个表格")

            table_data = self.extract_table_data(table)

            if table_data:
                table_info = {
                    '表格编号': table_idx + 1,
                    '表格行数': len(table_data),
                    '表格列数': len(table_data[0]) - 1 if table_data else 0,  # 减去行号列
                    '数据': table_data
                }
                all_tables_data.append(table_info)

        logger.info(f"总共处理了 {len(all_tables_data)} 个表格")
        self.tables_data = all_tables_data
        return all_tables_data

    def save_to_json(self, output_path: str, encoding: str = 'utf-8') -> bool:
        """
        将表格数据保存为 JSON 文件

        Args:
            output_path: 输出文件路径
            encoding: 文件编码，默认为 utf-8

        Returns:
            bool: 保存成功返回 True，失败返回 False
        """
        try:
            if not self.tables_data:
                logger.warning("没有数据可保存，请先调用 process_all_tables()")
                return False

            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 构建完整的数据结构
            output_data = {
                '文档信息': {
                    '源文件': self.file_path,
                    '表格数量': len(self.tables_data),
                    '处理时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                '表格数据': self.tables_data
            }

            # 保存为 JSON 文件
            with open(output_path, 'w', encoding=encoding) as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"数据已保存到: {output_path}")
            return True

        except Exception as e:
            logger.error(f"保存 JSON 文件失败: {str(e)}")
            return False

    def get_summary(self) -> Dict[str, Any]:
        """
        获取数据处理摘要

        Returns:
            Dict: 包含处理摘要的字典
        """
        if not self.tables_data:
            return {'状态': '未处理数据'}

        total_rows = sum(table['表格行数'] for table in self.tables_data)

        summary = {
            '文档路径': self.file_path,
            '表格总数': len(self.tables_data),
            '数据总行数': total_rows,
            '表格详情': []
        }

        for table in self.tables_data:
            table_summary = {
                '表格编号': table['表格编号'],
                '行数': table['表格行数'],
                '列数': table['表格列数']
            }
            summary['表格详情'].append(table_summary)

        return summary


def main():
    """主函数 - 演示如何使用 DiscretionDataProcessor"""

    # 文件路径
    docx_file = "datarw/data/4675378.docx"
    json_output = "datarw/data/4675378_parsed.json"

    # 创建处理器实例
    processor = DiscretionDataProcessor(docx_file)

    # 加载文档
    if not processor.load_document():
        logger.error("文档加载失败，程序退出")
        return

    # 处理所有表格
    tables_data = processor.process_all_tables()

    if not tables_data:
        logger.warning("未找到任何表格数据")
        return

    # 保存为 JSON
    if processor.save_to_json(json_output):
        logger.info("数据处理完成")
    else:
        logger.error("JSON 保存失败")
        return

    # 显示处理摘要
    summary = processor.get_summary()
    logger.info("处理摘要:")
    logger.info(json.dumps(summary, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()