"""
自由裁量权数据处理模块
用于读取和解析 Word 文档中的表格数据，并将其转换为 JSON 格式
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any
from docx import Document
from docx.table import Table
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DiscretionDataProcessor:
    """自由裁量权数据处理器"""

    def __init__(self, file_path: str):
        """
        初始化数据处理器

        Args:
            file_path: Word 文档文件路径
        """
        self.file_path = file_path
        self.document = None
        self.tables_data = []

    def load_document(self) -> bool:
        """
        加载 Word 文档

        Returns:
            bool: 加载成功返回 True，失败返回 False
        """
        try:
            if not os.path.exists(self.file_path):
                logger.error(f"文件不存在: {self.file_path}")
                return False

            self.document = Document(self.file_path)
            logger.info(f"成功加载文档: {self.file_path}")
            return True

        except Exception as e:
            logger.error(f"加载文档失败: {str(e)}")
            return False

    def extract_table_data(self, table: Table) -> List[Dict[str, Any]]:
        """
        从表格中提取数据

        Args:
            table: Word 文档中的表格对象

        Returns:
            List[Dict]: 表格数据列表，每行数据为一个字典
        """
        table_data = []

        if not table.rows:
            logger.warning("表格为空")
            return table_data

        # 获取表头
        header_row = table.rows[0]
        headers = []
        for cell in header_row.cells:
            header_text = cell.text.strip()
            headers.append(header_text if header_text else f"列_{len(headers) + 1}")

        logger.info(f"表格表头: {headers}")

        # 提取数据行
        for row_idx, row in enumerate(table.rows[1:], start=1):
            row_data = {}
            for col_idx, cell in enumerate(row.cells):
                if col_idx < len(headers):
                    cell_text = cell.text.strip()
                    row_data[headers[col_idx]] = cell_text

            # 只添加非空行
            if any(value.strip() for value in row_data.values()):
                row_data['行号'] = row_idx
                table_data.append(row_data)

        logger.info(f"提取到 {len(table_data)} 行数据")
        return table_data

    def process_all_tables(self) -> List[Dict[str, Any]]:
        """
        处理文档中的所有表格

        Returns:
            List[Dict]: 所有表格的数据
        """
        if not self.document:
            logger.error("文档未加载，请先调用 load_document()")
            return []

        all_tables_data = []

        for table_idx, table in enumerate(self.document.tables):
            logger.info(f"正在处理第 {table_idx + 1} 个表格")

            table_data = self.extract_table_data(table)

            if table_data:
                table_info = {
                    '表格编号': table_idx + 1,
                    '表格行数': len(table_data),
                    '表格列数': len(table_data[0]) - 1 if table_data else 0,  # 减去行号列
                    '数据': table_data
                }
                all_tables_data.append(table_info)

        logger.info(f"总共处理了 {len(all_tables_data)} 个表格")
        self.tables_data = all_tables_data
        return all_tables_data

    def save_to_json(self, output_path: str, encoding: str = 'utf-8') -> bool:
        """
        将表格数据保存为 JSON 文件

        Args:
            output_path: 输出文件路径
            encoding: 文件编码，默认为 utf-8

        Returns:
            bool: 保存成功返回 True，失败返回 False
        """
        try:
            if not self.tables_data:
                logger.warning("没有数据可保存，请先调用 process_all_tables()")
                return False

            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 构建完整的数据结构
            output_data = {
                '文档信息': {
                    '源文件': self.file_path,
                    '表格数量': len(self.tables_data),
                    '处理时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                '表格数据': self.tables_data
            }

            # 保存为 JSON 文件
            with open(output_path, 'w', encoding=encoding) as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"数据已保存到: {output_path}")
            return True

        except Exception as e:
            logger.error(f"保存 JSON 文件失败: {str(e)}")
            return False

    def get_summary(self) -> Dict[str, Any]:
        """
        获取数据处理摘要

        Returns:
            Dict: 包含处理摘要的字典
        """
        if not self.tables_data:
            return {'状态': '未处理数据'}

        total_rows = sum(table['表格行数'] for table in self.tables_data)

        summary = {
            '文档路径': self.file_path,
            '表格总数': len(self.tables_data),
            '数据总行数': total_rows,
            '表格详情': []
        }

        for table in self.tables_data:
            table_summary = {
                '表格编号': table['表格编号'],
                '行数': table['表格行数'],
                '列数': table['表格列数']
            }
            summary['表格详情'].append(table_summary)

        return summary

    def group_data_by_column(self, column_index: int = None, column_name: str = None) -> List[Dict[str, Any]]:
        """
        根据指定列的值对数据进行分组整合

        Args:
            column_index: 列索引（从0开始），与column_name二选一
            column_name: 列名称，与column_index二选一

        Returns:
            List[Dict]: 分组后的数据列表
        """
        if not self.tables_data:
            logger.error("没有数据可分组，请先调用 process_all_tables()")
            return []

        if column_index is None and column_name is None:
            logger.error("必须指定 column_index 或 column_name 参数")
            return []

        grouped_results = []

        for table_info in self.tables_data:
            table_data = table_info['数据']
            if not table_data:
                continue

            # 获取表头信息
            first_row = table_data[0]
            headers = [key for key in first_row.keys() if key != '行号']

            # 确定分组列
            group_column = None
            if column_name:
                if column_name in headers:
                    group_column = column_name
                else:
                    logger.error(f"列名 '{column_name}' 不存在于表格中。可用列名: {headers}")
                    continue
            elif column_index is not None:
                if 0 <= column_index < len(headers):
                    group_column = headers[column_index]
                else:
                    logger.error(f"列索引 {column_index} 超出范围。有效范围: 0-{len(headers)-1}")
                    continue

            logger.info(f"正在按列 '{group_column}' (索引: {headers.index(group_column)}) 进行分组")

            # 按指定列的值进行分组
            groups = {}
            for row in table_data:
                group_key = row.get(group_column, "").strip()

                # 跳过空值
                if not group_key:
                    continue

                if group_key not in groups:
                    groups[group_key] = {
                        '分组值': group_key,
                        '分组列': group_column,
                        '数据行数': 0,
                        '数据列表': []
                    }

                # 创建行数据副本（不包含行号）
                row_copy = {k: v for k, v in row.items() if k != '行号'}
                groups[group_key]['数据列表'].append(row_copy)
                groups[group_key]['数据行数'] += 1

            # 构建分组结果
            table_grouped = {
                '表格编号': table_info['表格编号'],
                '分组列': group_column,
                '分组数量': len(groups),
                '原始行数': len(table_data),
                '分组数据': list(groups.values())
            }

            grouped_results.append(table_grouped)
            logger.info(f"表格 {table_info['表格编号']} 按 '{group_column}' 分组完成，共 {len(groups)} 个分组")

        return grouped_results

    def save_grouped_data_to_json(self, grouped_data: List[Dict[str, Any]], output_path: str,
                                  encoding: str = 'utf-8') -> bool:
        """
        将分组后的数据保存为 JSON 文件

        Args:
            grouped_data: 分组后的数据
            output_path: 输出文件路径
            encoding: 文件编码，默认为 utf-8

        Returns:
            bool: 保存成功返回 True，失败返回 False
        """
        try:
            if not grouped_data:
                logger.warning("没有分组数据可保存")
                return False

            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 构建完整的数据结构
            output_data = {
                '文档信息': {
                    '源文件': self.file_path,
                    '处理时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    '数据类型': '分组数据'
                },
                '分组统计': {
                    '表格总数': len(grouped_data),
                    '总分组数': sum(table['分组数量'] for table in grouped_data)
                },
                '分组数据': grouped_data
            }

            # 保存为 JSON 文件
            with open(output_path, 'w', encoding=encoding) as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"分组数据已保存到: {output_path}")
            return True

        except Exception as e:
            logger.error(f"保存分组数据失败: {str(e)}")
            return False

    def get_grouped_summary(self, grouped_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取分组数据的摘要信息

        Args:
            grouped_data: 分组后的数据

        Returns:
            Dict: 包含分组摘要的字典
        """
        if not grouped_data:
            return {'状态': '无分组数据'}

        summary = {
            '文档路径': self.file_path,
            '表格总数': len(grouped_data),
            '分组详情': []
        }

        total_groups = 0
        total_rows = 0

        for table in grouped_data:
            table_summary = {
                '表格编号': table['表格编号'],
                '分组列': table['分组列'],
                '分组数量': table['分组数量'],
                '原始行数': table['原始行数'],
                '分组列表': []
            }

            # 添加每个分组的详细信息
            for group in table['分组数据']:
                group_info = {
                    '分组值': group['分组值'],
                    '数据行数': group['数据行数']
                }
                table_summary['分组列表'].append(group_info)
                total_rows += group['数据行数']

            total_groups += table['分组数量']
            summary['分组详情'].append(table_summary)

        summary['总分组数'] = total_groups
        summary['总数据行数'] = total_rows

        return summary


def main():
    """主函数 - 演示如何使用 DiscretionDataProcessor"""

    # 文件路径
    docx_file = "datarw/data/4675378.docx"
    json_output = "datarw/data/4675378_parsed.json"
    grouped_output = "datarw/data/4675378_grouped.json"

    # 创建处理器实例
    processor = DiscretionDataProcessor(docx_file)

    # 加载文档
    if not processor.load_document():
        logger.error("文档加载失败，程序退出")
        return

    # 处理所有表格
    tables_data = processor.process_all_tables()

    if not tables_data:
        logger.warning("未找到任何表格数据")
        return

    # 保存原始数据为 JSON
    if processor.save_to_json(json_output):
        logger.info("原始数据处理完成")
    else:
        logger.error("原始数据 JSON 保存失败")
        return

    # 显示原始数据处理摘要
    summary = processor.get_summary()
    logger.info("原始数据处理摘要:")
    logger.info(json.dumps(summary, ensure_ascii=False, indent=2))

    # 演示分组功能
    logger.info("\n" + "="*50)
    logger.info("开始演示数据分组功能")
    logger.info("="*50)

    # 按"违法行为"列进行分组（列名方式）
    logger.info("按 '违法行为' 列进行分组...")
    grouped_data = processor.group_data_by_column(column_name="违法行为")

    if grouped_data:
        # 保存分组数据
        if processor.save_grouped_data_to_json(grouped_data, grouped_output):
            logger.info("分组数据保存完成")
        else:
            logger.error("分组数据保存失败")

        # 显示分组摘要
        grouped_summary = processor.get_grouped_summary(grouped_data)
        logger.info("分组数据摘要:")
        logger.info(json.dumps(grouped_summary, ensure_ascii=False, indent=2))
    else:
        logger.warning("数据分组失败")

    # 演示按列索引分组（可选）
    logger.info("\n" + "-"*30)
    logger.info("演示按列索引分组（第4列 - 裁量阶次）...")
    grouped_by_index = processor.group_data_by_column(column_index=4)  # 第5列（索引4）是"裁量阶次"

    if grouped_by_index:
        # 保存按裁量阶次分组的数据
        penalty_grouped_output = "datarw/data/4675378_penalty_grouped.json"
        processor.save_grouped_data_to_json(grouped_by_index, penalty_grouped_output)

        penalty_summary = processor.get_grouped_summary(grouped_by_index)
        logger.info("按裁量阶次分组摘要:")
        logger.info(json.dumps(penalty_summary, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()