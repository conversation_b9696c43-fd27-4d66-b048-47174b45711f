# 自由裁量权数据处理模块

## 概述

`discretion_data.py` 模块用于读取和解析 Microsoft Word 文档中的表格数据，并将其转换为结构化的 JSON 格式。该模块专门设计用于处理自由裁量权相关的法律文档，能够自动提取表格中的违法行为、法律规定、处罚依据等信息。

## 功能特性

- ✅ **Word 文档读取**: 支持读取 `.docx` 格式的 Word 文档
- ✅ **表格数据提取**: 自动识别并提取文档中的所有表格
- ✅ **智能表头处理**: 自动识别表格表头，支持空表头的自动命名
- ✅ **数据清洗**: 自动过滤空行和无效数据
- ✅ **JSON 输出**: 将提取的数据转换为结构化的 JSON 格式
- ✅ **数据分组**: 支持按指定列对数据进行分组整合
- ✅ **灵活分组方式**: 支持按列名或列索引进行分组
- ✅ **分组数据保存**: 将分组后的数据保存为独立的 JSON 文件
- ✅ **详细日志**: 提供详细的中文处理日志
- ✅ **错误处理**: 完善的异常处理机制
- ✅ **数据摘要**: 提供处理结果和分组结果的统计摘要

## 安装依赖

```bash
pip install python-docx
```

## 使用方法

### 基本使用

```python
from datarw.discretion_data import DiscretionDataProcessor

# 创建处理器实例
processor = DiscretionDataProcessor("path/to/your/document.docx")

# 加载文档
if processor.load_document():
    # 处理所有表格
    tables_data = processor.process_all_tables()

    # 保存原始数据为 JSON
    processor.save_to_json("output.json")

    # 获取处理摘要
    summary = processor.get_summary()
    print(summary)
```

### 数据分组功能

```python
# 按列名分组（推荐方式）
grouped_data = processor.group_data_by_column(column_name="违法行为")

# 按列索引分组
grouped_data = processor.group_data_by_column(column_index=4)  # 第5列

# 保存分组数据
processor.save_grouped_data_to_json(grouped_data, "grouped_output.json")

# 获取分组摘要
grouped_summary = processor.get_grouped_summary(grouped_data)
print(grouped_summary)
```

### 命令行使用

```bash
# 直接运行模块，处理默认文件
python datarw/discretion_data.py
```

## 输出格式

### 原始数据格式

生成的原始 JSON 文件包含以下结构：

```json
{
  "文档信息": {
    "源文件": "datarw/data/4675378.docx",
    "表格数量": 1,
    "处理时间": "2025-07-31 16:18:15"
  },
  "表格数据": [
    {
      "表格编号": 1,
      "表格行数": 30,
      "表格列数": 8,
      "数据": [
        {
          "序号": "",
          "违法行为": "生产经营单位未按规定...",
          "法律规定": "【地方性法规】《广东省安全生产条例》...",
          "处罚依据": "【地方性法规】《广东省安全生产条例》...",
          "裁量阶次": "较轻",
          "适用条件": "未按规定对离岗六个月以上...",
          "具体标准": "责令限期改正，可以处2万元以下...",
          "备注": "",
          "行号": 1
        }
      ]
    }
  ]
}
```

### 分组数据格式

分组后的 JSON 文件包含以下结构：

```json
{
  "文档信息": {
    "源文件": "datarw/data/4675378.docx",
    "处理时间": "2025-07-31 17:03:26",
    "数据类型": "分组数据"
  },
  "分组统计": {
    "表格总数": 1,
    "总分组数": 10
  },
  "分组数据": [
    {
      "表格编号": 1,
      "分组列": "违法行为",
      "分组数量": 10,
      "原始行数": 30,
      "分组数据": [
        {
          "分组值": "生产经营单位未按规定对离岗六个月以上的从业人员进行专门安全生产教育和培训的",
          "分组列": "违法行为",
          "数据行数": 3,
          "数据列表": [
            {
              "序号": "",
              "违法行为": "生产经营单位未按规定...",
              "法律规定": "【地方性法规】《广东省安全生产条例》...",
              "裁量阶次": "较轻",
              "适用条件": "未按规定对离岗六个月以上...",
              "具体标准": "责令限期改正，可以处2万元以下...",
              "备注": ""
            }
          ]
        }
      ]
    }
  ]
}
```

## API 参考

### DiscretionDataProcessor 类

#### 构造函数

```python
DiscretionDataProcessor(file_path: str)
```

**参数:**
- `file_path`: Word 文档文件路径

#### 主要方法

##### load_document()

```python
def load_document() -> bool
```

加载 Word 文档。

**返回值:**
- `bool`: 加载成功返回 True，失败返回 False

##### process_all_tables()

```python
def process_all_tables() -> List[Dict[str, Any]]
```

处理文档中的所有表格。

**返回值:**
- `List[Dict]`: 包含所有表格数据的列表

##### save_to_json()

```python
def save_to_json(output_path: str, encoding: str = 'utf-8') -> bool
```

将表格数据保存为 JSON 文件。

**参数:**
- `output_path`: 输出文件路径
- `encoding`: 文件编码，默认为 utf-8

**返回值:**
- `bool`: 保存成功返回 True，失败返回 False

##### get_summary()

```python
def get_summary() -> Dict[str, Any]
```

获取数据处理摘要。

**返回值:**
- `Dict`: 包含处理摘要的字典

##### group_data_by_column()

```python
def group_data_by_column(column_index: int = None, column_name: str = None) -> List[Dict[str, Any]]
```

根据指定列的值对数据进行分组整合。

**参数:**
- `column_index`: 列索引（从0开始），与column_name二选一
- `column_name`: 列名称，与column_index二选一

**返回值:**
- `List[Dict]`: 分组后的数据列表

##### save_grouped_data_to_json()

```python
def save_grouped_data_to_json(grouped_data: List[Dict[str, Any]], output_path: str, encoding: str = 'utf-8') -> bool
```

将分组后的数据保存为 JSON 文件。

**参数:**
- `grouped_data`: 分组后的数据
- `output_path`: 输出文件路径
- `encoding`: 文件编码，默认为 utf-8

**返回值:**
- `bool`: 保存成功返回 True，失败返回 False

##### get_grouped_summary()

```python
def get_grouped_summary(grouped_data: List[Dict[str, Any]]) -> Dict[str, Any]
```

获取分组数据的摘要信息。

**参数:**
- `grouped_data`: 分组后的数据

**返回值:**
- `Dict`: 包含分组摘要的字典

## 测试

运行测试脚本：

```bash
python datarw/test_discretion_data.py
```

测试包括：
- 单元测试：测试各个功能模块
- 集成测试：测试完整的处理流程

## 日志输出

模块提供详细的中文日志输出，包括：

```
2025-07-31 16:18:15,466 - INFO - 表格表头: ['序号', '违法行为', '法律规定', ...]
2025-07-31 16:18:15,514 - INFO - 提取到 30 行数据
2025-07-31 16:18:15,515 - INFO - 总共处理了 1 个表格
2025-07-31 16:18:15,517 - INFO - 数据已保存到: datarw/data/4675378_parsed.json
```

## 错误处理

模块包含完善的错误处理机制：

- 文件不存在检查
- 文档格式验证
- 表格结构验证
- JSON 保存异常处理

## 注意事项

1. **文件格式**: 仅支持 `.docx` 格式的 Word 文档
2. **表格结构**: 假设第一行为表头
3. **编码**: 输出 JSON 文件使用 UTF-8 编码
4. **内存使用**: 大型文档可能占用较多内存
5. **表格复杂度**: 不支持合并单元格的复杂表格结构

## 扩展功能

可以根据需要扩展以下功能：

- 支持 `.doc` 格式文档
- 处理合并单元格
- 添加数据验证规则
- 支持多种输出格式（Excel、CSV 等）
- 批量处理多个文档

## 版本历史

- **v1.0.0** (2025-07-31): 初始版本，支持基本的 Word 表格解析和 JSON 输出功能
